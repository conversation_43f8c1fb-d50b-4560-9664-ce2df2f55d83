<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Believe Broadband - High-Speed Internet Solutions</title>
    <!-- 
    IMAGE PLACEHOLDER REPLACEMENT GUIDE:
    
    To replace image placeholders with actual images:
    1. Replace <div class="img-placeholder [class-name]">...</div> with <img src="your-image.jpg" alt="Description" class="[class-name]">
    2. For the logo: Replace the logo-placeholder div with <img src="logo.png" alt="Believe Broadband" class="logo-image">
    
    Image recommendations:
    - Logo: 200x50px (PNG with transparent background)
    - Hero Image: 1200x300px (Network/technology visual)
    - Feature Icons: 80x80px (Speed, reliability, support icons)
    - About Image: 500x400px (Team/office/technology photo)
    - Service Images: 400x200px (Home, office, data center photos)
    - Team Photos: 300x250px (Professional headshots)
    - Customer Photos: 80x80px (Customer testimonial photos)
    - Infrastructure Gallery: 400x200px (Network equipment, installations)
    -->
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Arial", sans-serif;
        line-height: 1.6;
        color: #333;
        background-color: #ffffff;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
      }

      /* Header */
      header {
        background: #000;
        padding: 1rem 0;
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 1000;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      nav {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .logo {
        display: flex;
        align-items: center;
        gap: 15px;
      }

      .logo-image {
        height: 60px;
        width: auto;
        max-width: 250px;
        object-fit: contain;
      }

      .logo-placeholder {
        width: 200px;
        height: 50px;
        background: #333;
        border: 2px dashed #ff0000;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #ff0000;
        font-size: 12px;
        text-align: center;
        border-radius: 5px;
      }

      .nav-links {
        display: flex;
        list-style: none;
        gap: 30px;
      }

      .nav-links a {
        color: white;
        text-decoration: none;
        font-weight: 500;
        transition: color 0.3s ease;
      }

      .nav-links a:hover {
        color: #ff0000;
      }

      .cta-button {
        background: #ff0000;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        font-weight: bold;
        cursor: pointer;
        transition: background 0.3s ease;
      }

      .cta-button:hover {
        background: #cc0000;
      }

      /* Hero Section */
      .hero {
        background: linear-gradient(
            135deg,
            rgba(0, 0, 0, 0.8) 0%,
            rgba(51, 51, 51, 0.8) 100%
          ),
          url("images/hero.png");
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
        color: white;
        padding: 150px 0 100px;
        text-align: center;
        position: relative;
        overflow: hidden;
      }

      .hero::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          45deg,
          rgba(255, 0, 0, 0.1) 0%,
          rgba(0, 0, 0, 0.3) 100%
        );
        z-index: 1;
      }

      .hero .container {
        position: relative;
        z-index: 2;
      }

      .hero h1 {
        font-size: 4rem;
        margin-bottom: 20px;
        font-weight: bold;
        letter-spacing: 2px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        animation: fadeInUp 1s ease-out;
      }

      .hero p {
        font-size: 1.4rem;
        margin-bottom: 30px;
        max-width: 700px;
        margin-left: auto;
        margin-right: auto;
        opacity: 0.95;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        animation: fadeInUp 1s ease-out 0.3s both;
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .hero-buttons {
        display: flex;
        gap: 20px;
        justify-content: center;
        flex-wrap: wrap;
        animation: fadeInUp 1s ease-out 0.6s both;
      }

      .btn-primary {
        background: linear-gradient(45deg, #ff0000, #cc0000);
        color: white;
        padding: 15px 30px;
        border: none;
        border-radius: 8px;
        font-size: 1.1rem;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        box-shadow: 0 4px 15px rgba(255, 0, 0, 0.3);
        position: relative;
        overflow: hidden;
      }

      .btn-primary::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        transition: left 0.5s;
      }

      .btn-primary:hover::before {
        left: 100%;
      }

      .btn-primary:hover {
        background: linear-gradient(45deg, #cc0000, #990000);
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(255, 0, 0, 0.4);
      }

      .btn-secondary {
        background: transparent;
        color: white;
        padding: 15px 30px;
        border: 2px solid white;
        border-radius: 8px;
        font-size: 1.1rem;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
      }

      .btn-secondary:hover {
        background: white;
        color: #000;
      }

      /* Image Placeholders */
      .img-placeholder {
        background: #f0f0f0;
        border: 2px dashed #ccc;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #666;
        font-size: 14px;
        text-align: center;
        border-radius: 10px;
        position: relative;
      }

      .img-placeholder.dark {
        background: #333;
        border-color: #555;
        color: #999;
      }

      .hero-image-placeholder {
        width: 100%;
        height: 300px;
        margin-top: 50px;
      }

      .feature-image-placeholder {
        width: 80px;
        height: 80px;
        margin: 0 auto 20px;
        border-radius: 50%;
      }

      .about-image-placeholder {
        width: 100%;
        height: 400px;
      }

      .service-image-placeholder {
        width: 100%;
        height: 200px;
        margin-bottom: 20px;
      }

      .testimonial-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        margin: 0 auto 15px;
      }

      .team-image-placeholder {
        width: 100%;
        height: 250px;
        margin-bottom: 20px;
      }

      .gallery-image-placeholder {
        width: 100%;
        height: 200px;
      }
      .features {
        padding: 100px 0;
        background: #f8f9fa;
      }

      .features h2 {
        text-align: center;
        font-size: 2.5rem;
        margin-bottom: 60px;
        color: #000;
      }

      .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 40px;
      }

      .feature-card {
        background: white;
        padding: 40px 30px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        text-align: center;
        transition: transform 0.3s ease;
      }

      .feature-card:hover {
        transform: translateY(-10px);
      }

      .feature-image-placeholder {
        width: 80px;
        height: 80px;
        margin: 0 auto 20px;
        border-radius: 50%;
      }

      .feature-card h3 {
        font-size: 1.5rem;
        margin-bottom: 15px;
        color: #000;
      }

      .feature-card p {
        color: #666;
        line-height: 1.6;
      }

      /* About Section */
      .about {
        padding: 100px 0;
        background: white;
      }

      .about-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 60px;
        align-items: center;
      }

      .about-text h2 {
        font-size: 2.5rem;
        margin-bottom: 30px;
        color: #000;
      }

      .about-text p {
        font-size: 1.1rem;
        margin-bottom: 20px;
        color: #666;
        line-height: 1.8;
      }

      .about-stats {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
        margin-top: 40px;
      }

      .stat {
        text-align: center;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 10px;
      }

      .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        color: #ff0000;
        display: block;
      }

      .stat-label {
        color: #666;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      .about-image-placeholder {
        width: 100%;
        height: 400px;
      }

      /* Services Section */
      .services {
        padding: 100px 0;
        background: #000;
        color: white;
      }

      .services h2 {
        text-align: center;
        font-size: 2.5rem;
        margin-bottom: 60px;
      }

      .services-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 40px;
      }

      .service-card {
        background: #111;
        padding: 40px;
        border-radius: 15px;
        border: 1px solid #333;
        transition: all 0.3s ease;
      }

      .service-card:hover {
        border-color: #ff0000;
        background: #1a1a1a;
      }

      .service-card h3 {
        font-size: 1.5rem;
        margin-bottom: 15px;
        color: #ff0000;
      }

      .service-card p {
        margin-bottom: 20px;
        opacity: 0.9;
        line-height: 1.6;
      }

      .service-features {
        list-style: none;
        margin-bottom: 30px;
      }

      .service-features li {
        padding: 5px 0;
        position: relative;
        padding-left: 20px;
      }

      .service-features li:before {
        content: "✓";
        color: #ff0000;
        font-weight: bold;
        position: absolute;
        left: 0;
      }

      .price {
        font-size: 2rem;
        font-weight: bold;
        color: #ff0000;
        margin-bottom: 20px;
      }

      /* Testimonials Section */
      .testimonials {
        padding: 100px 0;
        background: #f8f9fa;
      }

      .testimonials h2 {
        text-align: center;
        font-size: 2.5rem;
        margin-bottom: 60px;
        color: #000;
      }

      .testimonials-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 40px;
      }

      .testimonial-card {
        background: white;
        padding: 40px 30px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        text-align: center;
      }

      .testimonial-card p {
        font-style: italic;
        margin-bottom: 20px;
        color: #666;
        font-size: 1.1rem;
        line-height: 1.6;
      }

      .testimonial-card h4 {
        color: #000;
        margin-bottom: 5px;
      }

      .testimonial-card span {
        color: #ff0000;
        font-size: 0.9rem;
      }

      /* Team Section */
      .team {
        padding: 100px 0;
        background: white;
      }

      .team h2 {
        text-align: center;
        font-size: 2.5rem;
        margin-bottom: 60px;
        color: #000;
      }

      .team-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 40px;
      }

      .team-card {
        text-align: center;
        background: #f8f9fa;
        padding: 30px;
        border-radius: 15px;
        transition: transform 0.3s ease;
      }

      .team-card:hover {
        transform: translateY(-10px);
      }

      .team-card h3 {
        margin-bottom: 10px;
        color: #000;
      }

      .team-card p {
        color: #ff0000;
        font-weight: bold;
        margin-bottom: 10px;
      }

      .team-card span {
        color: #666;
        font-size: 0.9rem;
      }

      /* Gallery Section */
      .gallery {
        padding: 100px 0;
        background: #000;
      }

      .gallery h2 {
        text-align: center;
        font-size: 2.5rem;
        margin-bottom: 60px;
        color: white;
      }

      .gallery-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
      }

      /* Footer */
      footer {
        background: #000;
        color: white;
        padding: 60px 0 30px;
      }

      .footer-content {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 40px;
        margin-bottom: 40px;
      }

      .footer-section h3 {
        margin-bottom: 20px;
        color: #ff0000;
      }

      .footer-section ul {
        list-style: none;
      }

      .footer-section ul li {
        margin-bottom: 10px;
      }

      .footer-section ul li a {
        color: #ccc;
        text-decoration: none;
        transition: color 0.3s ease;
      }

      .footer-section ul li a:hover {
        color: #ff0000;
      }

      .footer-bottom {
        border-top: 1px solid #333;
        padding-top: 30px;
        text-align: center;
        color: #666;
      }

      /* Responsive */
      @media (max-width: 768px) {
        .nav-links {
          display: none;
        }

        .hero h1 {
          font-size: 2.5rem;
        }

        .about-content {
          grid-template-columns: 1fr;
        }

        .hero-buttons {
          flex-direction: column;
          align-items: center;
        }

        .about-stats {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </head>
  <body>
    <!-- Header -->
    <header>
      <nav class="container">
        <div class="logo">
          <!-- Logo image -->
          <img
            src="images/logo.png"
            alt="Believe Broadband"
            class="logo-image"
          />
        </div>
        <ul class="nav-links">
          <li><a href="#home">Home</a></li>
          <li><a href="#about">About</a></li>
          <li><a href="#services">Services</a></li>
          <li><a href="#contact">Contact</a></li>
        </ul>
        <button class="cta-button">Get Connected</button>
      </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
      <div class="container">
        <h1>Lightning-Fast Internet</h1>
        <p>
          Experience the power of reliable, high-speed broadband that keeps you
          connected to what matters most. Join thousands of satisfied customers
          who believe in better connectivity.
        </p>
        <div class="hero-buttons">
          <a href="#services" class="btn-primary">View Plans</a>
          <a href="#about" class="btn-secondary">Learn More</a>
        </div>
      </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials">
      <div class="container">
        <h2>What Our Customers Say</h2>
        <div class="testimonials-grid">
          <div class="testimonial-card">
            <img
              src="images/customer1.jpg"
              alt="Sarah Johnson"
              class="testimonial-avatar"
              style="
                width: 80px;
                height: 80px;
                border-radius: 50%;
                margin: 0 auto 15px;
                object-fit: cover;
                display: block;
              "
            />
            <p>
              "Believe Broadband transformed our home office setup. The speeds
              are incredible and support is always helpful!"
            </p>
            <h4>Sarah Johnson</h4>
            <span>Remote Worker</span>
          </div>
          <div class="testimonial-card">
            <img
              src="images/customer2.jpg"
              alt="Mike Chen"
              class="testimonial-avatar"
              style="
                width: 80px;
                height: 80px;
                border-radius: 50%;
                margin: 0 auto 15px;
                object-fit: cover;
                display: block;
              "
            />
            <p>
              "Our business productivity increased dramatically after switching.
              Reliable service we can count on."
            </p>
            <h4>Mike Chen</h4>
            <span>Business Owner</span>
          </div>
          <div class="testimonial-card">
            <img
              src="images/customer3.jpg"
              alt="Emma Rodriguez"
              class="testimonial-avatar"
              style="
                width: 80px;
                height: 80px;
                border-radius: 50%;
                margin: 0 auto 15px;
                object-fit: cover;
                display: block;
              "
            />
            <p>
              "Finally, an ISP that delivers on their promises. Fast
              installation and even faster internet!"
            </p>
            <h4>Emma Rodriguez</h4>
            <span>Satisfied Customer</span>
          </div>
        </div>
      </div>
    </section>

    <!-- Team Section -->
    <section class="team">
      <div class="container">
        <h2>Meet Our Team</h2>
        <div class="team-grid">
          <div class="team-card">
            <img
              src="images/team2.jpg"
              alt="John Smith"
              style="
                width: 100%;
                height: 250px;
                object-fit: cover;
                margin-bottom: 20px;
                border-radius: 10px;
              "
            />
            <h3>John Smith</h3>
            <p>Chief Technology Officer</p>
            <span>Leading our technical innovation</span>
          </div>
          <div class="team-card">
            <img
              src="images/team1.jpg"
              alt="Lisa Williams"
              style="
                width: 100%;
                height: 250px;
                object-fit: cover;
                margin-bottom: 20px;
                border-radius: 10px;
              "
            />
            <h3>Lisa Williams</h3>
            <p>Customer Success Manager</p>
            <span>Ensuring exceptional service</span>
          </div>
          <div class="team-card">
            <img
              src="images/team3.jpg"
              alt="David Brown"
              style="
                width: 100%;
                height: 250px;
                object-fit: cover;
                margin-bottom: 20px;
                border-radius: 10px;
              "
            />
            <h3>David Brown</h3>
            <p>Network Operations Director</p>
            <span>Maintaining our infrastructure</span>
          </div>
        </div>
      </div>
    </section>

    <!-- Gallery Section -->
    <section class="gallery">
      <div class="container">
        <h2>Our Infrastructure</h2>
        <div class="gallery-grid">
          <img
            src="images/data-center.jpg"
            alt="Data Center Infrastructure"
            style="
              width: 100%;
              height: 200px;
              object-fit: cover;
              border-radius: 10px;
            "
          />
          <img
            src="images/server-rack.jpg"
            alt="Server Rack Equipment"
            style="
              width: 100%;
              height: 200px;
              object-fit: cover;
              border-radius: 10px;
            "
          />
          <img
            src="images/communication-tower.jpg"
            alt="Communication Tower"
            style="
              width: 100%;
              height: 200px;
              object-fit: cover;
              border-radius: 10px;
            "
          />
          <img
            src="images/network-engineer.jpg"
            alt="Network Engineer at Work"
            style="
              width: 100%;
              height: 200px;
              object-fit: cover;
              border-radius: 10px;
            "
          />
          <img
            src="images/installation.jpg"
            alt="Installation Service"
            style="
              width: 100%;
              height: 200px;
              object-fit: cover;
              border-radius: 10px;
            "
          />
          <img
            src="images/giber-optic-macro.jpg"
            alt="Fiber Optic Technology"
            style="
              width: 100%;
              height: 200px;
              object-fit: cover;
              border-radius: 10px;
            "
          />
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features">
      <div class="container">
        <h2>Why Choose Believe Broadband?</h2>
        <div class="features-grid">
          <div class="feature-card">
            <img
              src="images/speed.png"
              alt="Speed Icon"
              style="
                width: 80px;
                height: 80px;
                margin: 0 auto 20px;
                display: block;
                object-fit: contain;
              "
            />
            <h3>Ultra-Fast Speeds</h3>
            <p>
              Experience blazing-fast internet speeds up to 1 Gbps. Stream,
              game, and work without any interruptions or buffering delays.
            </p>
          </div>
          <div class="feature-card">
            <img
              src="images/reliability.png"
              alt="Reliability Icon"
              style="
                width: 80px;
                height: 80px;
                margin: 0 auto 20px;
                display: block;
                object-fit: contain;
              "
            />
            <h3>99.9% Uptime</h3>
            <p>
              Reliable connection you can count on. Our robust infrastructure
              ensures you stay connected when it matters most.
            </p>
          </div>
          <div class="feature-card">
            <img
              src="images/support.png"
              alt="Support Icon"
              style="
                width: 80px;
                height: 80px;
                margin: 0 auto 20px;
                display: block;
                object-fit: contain;
              "
            />
            <h3>Award-Winning Support</h3>
            <p>
              24/7 customer support from local experts who understand your
              needs. Get help when you need it, how you need it.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- About Section -->
    <section class="about" id="about">
      <div class="container">
        <div class="about-content">
          <div class="about-text">
            <h2>About Believe Broadband</h2>
            <p>
              For over a decade, Believe Broadband has been at the forefront of
              delivering cutting-edge internet solutions to homes and
              businesses. We believe that fast, reliable internet isn't just a
              luxury—it's a necessity in today's connected world.
            </p>
            <p>
              Our commitment goes beyond just providing internet service. We're
              dedicated to empowering communities, enabling remote work,
              supporting online education, and bringing people together through
              technology.
            </p>
            <p>
              With state-of-the-art fiber-optic infrastructure and a
              customer-first approach, we're not just an internet service
              provider—we're your technology partner for the future.
            </p>
            <div class="about-stats">
              <div class="stat">
                <span class="stat-number">50K+</span>
                <span class="stat-label">Happy Customers</span>
              </div>
              <div class="stat">
                <span class="stat-number">99.9%</span>
                <span class="stat-label">Network Uptime</span>
              </div>
              <div class="stat">
                <span class="stat-number">24/7</span>
                <span class="stat-label">Expert Support</span>
              </div>
              <div class="stat">
                <span class="stat-number">10+</span>
                <span class="stat-label">Years Experience</span>
              </div>
            </div>
          </div>
          <img
            src="images/about-us.jpg"
            alt="About Believe Broadband"
            style="
              width: 100%;
              height: 400px;
              object-fit: cover;
              border-radius: 15px;
            "
          />
        </div>
      </div>
    </section>

    <!-- Services Section -->
    <section class="services" id="services">
      <div class="container">
        <h2>Our Featured Services</h2>
        <div class="services-grid">
          <div class="service-card">
            <img
              src="images/residential.jpg"
              alt="Residential Internet Service"
              style="
                width: 100%;
                height: 200px;
                object-fit: cover;
                margin-bottom: 20px;
                border-radius: 10px;
              "
            />
            <h3>Residential Plans</h3>
            <p>
              Perfect for homes and families who need reliable internet for
              streaming, gaming, and everyday use.
            </p>
            <ul class="service-features">
              <li>Speeds up to 1 Gbps</li>
              <li>Unlimited data usage</li>
              <li>Free installation</li>
              <li>Wi-Fi router included</li>
              <li>24/7 customer support</li>
            </ul>
            <div class="price">Starting at $49/mo</div>
            <button class="btn-primary">View Plans</button>
          </div>
          <div class="service-card">
            <img
              src="images/business.jpg"
              alt="Business Internet Solutions"
              style="
                width: 100%;
                height: 200px;
                object-fit: cover;
                margin-bottom: 20px;
                border-radius: 10px;
              "
            />
            <h3>Business Solutions</h3>
            <p>
              Scalable internet solutions designed to meet the demanding needs
              of modern businesses.
            </p>
            <ul class="service-features">
              <li>Dedicated fiber connections</li>
              <li>Static IP addresses</li>
              <li>Priority technical support</li>
              <li>Service level agreements</li>
              <li>Custom configurations</li>
            </ul>
            <div class="price">Starting at $99/mo</div>
            <button class="btn-primary">Contact Sales</button>
          </div>
          <div class="service-card">
            <img
              src="images/enterprise.jpg"
              alt="Enterprise Network Solutions"
              style="
                width: 100%;
                height: 200px;
                object-fit: cover;
                margin-bottom: 20px;
                border-radius: 10px;
              "
            />
            <h3>Enterprise Network</h3>
            <p>
              Advanced networking solutions for large organizations requiring
              maximum performance and security.
            </p>
            <ul class="service-features">
              <li>Multi-gigabit speeds</li>
              <li>Redundant connections</li>
              <li>Advanced security features</li>
              <li>Dedicated account manager</li>
              <li>Custom SLA options</li>
            </ul>
            <div class="price">Custom Pricing</div>
            <button class="btn-primary">Get Quote</button>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer id="contact">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h3>Contact Us</h3>
            <ul>
              <li>📞 1-800-BELIEVE</li>
              <li>📧 <EMAIL></li>
              <li>📍 123 Tech Street, Digital City</li>
              <li>🕒 24/7 Customer Support</li>
            </ul>
          </div>
          <div class="footer-section">
            <h3>Services</h3>
            <ul>
              <li><a href="#">Residential Internet</a></li>
              <li><a href="#">Business Solutions</a></li>
              <li><a href="#">Enterprise Network</a></li>
              <li><a href="#">Technical Support</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h3>Company</h3>
            <ul>
              <li><a href="#">About Us</a></li>
              <li><a href="#">Careers</a></li>
              <li><a href="#">News & Updates</a></li>
              <li><a href="#">Community</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h3>Support</h3>
            <ul>
              <li><a href="#">Help Center</a></li>
              <li><a href="#">Network Status</a></li>
              <li><a href="#">Speed Test</a></li>
              <li><a href="#">Billing</a></li>
            </ul>
          </div>
        </div>
        <div class="footer-bottom">
          <p>
            &copy; 2025 Believe Broadband. All rights reserved. | Privacy Policy
            | Terms of Service
          </p>
        </div>
      </div>
    </footer>

    <script>
      // Smooth scrolling for navigation links
      document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
        anchor.addEventListener("click", function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute("href"));
          if (target) {
            target.scrollIntoView({
              behavior: "smooth",
              block: "start",
            });
          }
        });
      });

      // Header background change on scroll
      window.addEventListener("scroll", function () {
        const header = document.querySelector("header");
        if (window.scrollY > 100) {
          header.style.background = "rgba(0, 0, 0, 0.95)";
        } else {
          header.style.background = "#000";
        }
      });

      // Add animation on scroll for feature cards
      const observerOptions = {
        threshold: 0.1,
        rootMargin: "0px 0px -50px 0px",
      };

      const observer = new IntersectionObserver(function (entries) {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.style.opacity = "1";
            entry.target.style.transform = "translateY(0)";
          }
        });
      }, observerOptions);

      // Observe feature cards
      document.querySelectorAll(".feature-card").forEach((card) => {
        card.style.opacity = "0";
        card.style.transform = "translateY(30px)";
        card.style.transition = "opacity 0.6s ease, transform 0.6s ease";
        observer.observe(card);
      });
    </script>
  </body>
</html>
